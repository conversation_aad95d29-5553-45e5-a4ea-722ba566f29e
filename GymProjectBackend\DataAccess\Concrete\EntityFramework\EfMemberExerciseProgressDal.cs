using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberExerciseProgressDal : EfEntityRepositoryBase<MemberExerciseProgress, GymContext>, IMemberExerciseProgressDal
    {
        public MemberExerciseProgressDto GetProgressByProgram(int userId, int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                var result = (from mep in context.MemberExerciseProgress
                             join mwp in context.MemberWorkoutPrograms on mep.MemberWorkoutProgramID equals mwp.MemberWorkoutProgramID
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where m.UserID == userId && mep.MemberWorkoutProgramID == memberWorkoutProgramId && mwp.IsActive == true && m.IsActive == true
                             select new
                             {
                                 Progress = mep,
                                 ProgramTemplateId = wpt.WorkoutProgramTemplateID
                             }).FirstOrDefault();

                if (result == null) return null;

                // Güncel günün bilgilerini al
                var currentDay = (from wpd in context.WorkoutProgramDays
                                 where wpd.WorkoutProgramTemplateID == result.ProgramTemplateId && wpd.DayNumber == result.Progress.CurrentDayNumber
                                 select wpd).FirstOrDefault();

                if (currentDay == null) return null;

                // Güncel günün egzersiz sayısını al
                var totalExercisesInDay = (from wpe in context.WorkoutProgramExercises
                                          where wpe.WorkoutProgramDayID == currentDay.WorkoutProgramDayID
                                          select wpe.WorkoutProgramExerciseID).Count();

                // Toplam gün sayısını al
                var totalDaysInProgram = context.WorkoutProgramDays
                    .Where(wpd => wpd.WorkoutProgramTemplateID == result.ProgramTemplateId)
                    .Count();

                // Tamamlanan egzersizleri parse et
                var completedExerciseIds = new List<int>();
                if (!string.IsNullOrEmpty(result.Progress.CompletedExercises))
                {
                    try
                    {
                        completedExerciseIds = JsonSerializer.Deserialize<List<int>>(result.Progress.CompletedExercises) ?? new List<int>();
                    }
                    catch
                    {
                        completedExerciseIds = new List<int>();
                    }
                }

                var completedCount = completedExerciseIds.Count;
                var progressPercentage = totalExercisesInDay > 0 ? (double)completedCount / totalExercisesInDay * 100 : 0;

                return new MemberExerciseProgressDto
                {
                    MemberWorkoutProgramID = result.Progress.MemberWorkoutProgramID,
                    CurrentDayNumber = result.Progress.CurrentDayNumber,
                    CurrentDayName = currentDay.DayName,
                    IsCurrentDayCompleted = result.Progress.IsCurrentDayCompleted,
                    IsRestDay = currentDay.IsRestDay,
                    LastCompletedDate = result.Progress.LastCompletedDate,
                    CompletedExerciseIds = completedExerciseIds,
                    TotalExercisesInDay = totalExercisesInDay,
                    CompletedExercisesCount = completedCount,
                    ProgressPercentage = progressPercentage,
                    IsProgramCompleted = result.Progress.IsProgramCompleted,
                    ProgramCompletedDate = result.Progress.ProgramCompletedDate,
                    TotalDaysInProgram = totalDaysInProgram,
                    CanCompleteDay = !currentDay.IsRestDay && completedCount == totalExercisesInDay
                };
            }
        }

        public List<MemberExerciseProgressDto> GetAllProgressByUser(int userId)
        {
            using (GymContext context = new GymContext())
            {
                var results = from mep in context.MemberExerciseProgress
                             join mwp in context.MemberWorkoutPrograms on mep.MemberWorkoutProgramID equals mwp.MemberWorkoutProgramID
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where m.UserID == userId && mwp.IsActive == true && m.IsActive == true
                             orderby mwp.StartDate descending
                             select new
                             {
                                 Progress = mep,
                                 ProgramTemplateId = wpt.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName
                             };

                var progressList = new List<MemberExerciseProgressDto>();

                foreach (var result in results)
                {
                    var progressDto = GetProgressByProgram(userId, result.Progress.MemberWorkoutProgramID);
                    if (progressDto != null)
                    {
                        progressList.Add(progressDto);
                    }
                }

                return progressList;
            }
        }

        public MemberExerciseProgress GetProgressEntityByProgram(int userId, int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                return (from mep in context.MemberExerciseProgress
                       join mwp in context.MemberWorkoutPrograms on mep.MemberWorkoutProgramID equals mwp.MemberWorkoutProgramID
                       join m in context.Members on mwp.MemberID equals m.MemberID
                       where m.UserID == userId && mep.MemberWorkoutProgramID == memberWorkoutProgramId && mwp.IsActive == true && m.IsActive == true
                       select mep).FirstOrDefault();
            }
        }

        public void CreateProgressRecord(int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                // Zaten var mı kontrol et
                var existing = context.MemberExerciseProgress
                    .FirstOrDefault(mep => mep.MemberWorkoutProgramID == memberWorkoutProgramId);

                if (existing == null)
                {
                    var progress = new MemberExerciseProgress
                    {
                        MemberWorkoutProgramID = memberWorkoutProgramId,
                        CurrentDayNumber = 1,
                        IsCurrentDayCompleted = false,
                        CompletedExercises = "[]",
                        IsProgramCompleted = false,
                        CreationDate = DateTime.Now
                    };

                    context.MemberExerciseProgress.Add(progress);
                    context.SaveChanges();
                }
            }
        }

        public void DeleteProgressRecord(int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                var progress = context.MemberExerciseProgress
                    .FirstOrDefault(mep => mep.MemberWorkoutProgramID == memberWorkoutProgramId);

                if (progress != null)
                {
                    context.MemberExerciseProgress.Remove(progress);
                    context.SaveChanges();
                }
            }
        }
    }
}
