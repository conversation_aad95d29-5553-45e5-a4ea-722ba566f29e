/// Workout Program Models - GymKod Pro Mobile
///
/// Bu dosya backend'deki antrenman programı DTO'larına uygun modeller içerir.
/// Referans: GymProjectBackend/Entities/DTOs/MemberWorkoutProgramDto.cs
library;

import 'package:json_annotation/json_annotation.dart';

part 'workout_program_models.g.dart';

/// Mobil API için aktif antrenman programı modeli
/// Backend: MemberActiveWorkoutProgramDto
@JsonSerializable()
class MemberActiveWorkoutProgramModel {
  /// Program atama ID'si
  final int memberWorkoutProgramID;
  
  /// Program şablon ID'si
  final int workoutProgramTemplateID;
  
  /// Program adı
  final String programName;
  
  /// Program açıklaması
  final String? programDescription;
  
  /// Den<PERSON>im seviyesi (Başlangıç, Orta, İleri)
  final String? experienceLevel;
  
  /// Hedef (Kilo Alma, Kilo Verme, Kas <PERSON>)
  final String? targetGoal;
  
  /// Program başlangıç tarihi
  final DateTime startDate;
  
  /// Program bitiş tarihi
  final DateTime? endDate;
  
  /// Atama notları
  final String? notes;
  
  /// Program gün sayısı
  final int dayCount;
  
  /// Program egzersiz sayısı
  final int exerciseCount;

  const MemberActiveWorkoutProgramModel({
    required this.memberWorkoutProgramID,
    required this.workoutProgramTemplateID,
    required this.programName,
    this.programDescription,
    this.experienceLevel,
    this.targetGoal,
    required this.startDate,
    this.endDate,
    this.notes,
    required this.dayCount,
    required this.exerciseCount,
  });

  /// JSON'dan model oluştur
  factory MemberActiveWorkoutProgramModel.fromJson(Map<String, dynamic> json) =>
      _$MemberActiveWorkoutProgramModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$MemberActiveWorkoutProgramModelToJson(this);

  /// Model kopyalama
  MemberActiveWorkoutProgramModel copyWith({
    int? memberWorkoutProgramID,
    int? workoutProgramTemplateID,
    String? programName,
    String? programDescription,
    String? experienceLevel,
    String? targetGoal,
    DateTime? startDate,
    DateTime? endDate,
    String? notes,
    int? dayCount,
    int? exerciseCount,
  }) {
    return MemberActiveWorkoutProgramModel(
      memberWorkoutProgramID: memberWorkoutProgramID ?? this.memberWorkoutProgramID,
      workoutProgramTemplateID: workoutProgramTemplateID ?? this.workoutProgramTemplateID,
      programName: programName ?? this.programName,
      programDescription: programDescription ?? this.programDescription,
      experienceLevel: experienceLevel ?? this.experienceLevel,
      targetGoal: targetGoal ?? this.targetGoal,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      notes: notes ?? this.notes,
      dayCount: dayCount ?? this.dayCount,
      exerciseCount: exerciseCount ?? this.exerciseCount,
    );
  }

  /// Equality
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MemberActiveWorkoutProgramModel &&
        other.memberWorkoutProgramID == memberWorkoutProgramID &&
        other.workoutProgramTemplateID == workoutProgramTemplateID &&
        other.programName == programName;
  }

  @override
  int get hashCode {
    return memberWorkoutProgramID.hashCode ^
        workoutProgramTemplateID.hashCode ^
        programName.hashCode;
  }

  @override
  String toString() {
    return 'MemberActiveWorkoutProgramModel(id: $memberWorkoutProgramID, name: $programName, days: $dayCount, exercises: $exerciseCount)';
  }

  /// Program aktif mi kontrol et
  bool get isActive {
    final now = DateTime.now();
    if (endDate == null) return true;
    return now.isBefore(endDate!) || now.isAtSameMomentAs(endDate!);
  }

  /// Program kaç gün kaldı
  int? get remainingDays {
    if (endDate == null) return null;
    final now = DateTime.now();
    final difference = endDate!.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  /// Deneyim seviyesi rengi (UI için)
  String get experienceLevelColor {
    switch (experienceLevel?.toLowerCase()) {
      case 'başlangıç':
        return '#4CAF50'; // Yeşil
      case 'orta':
        return '#FF9800'; // Turuncu
      case 'ileri':
        return '#F44336'; // Kırmızı
      default:
        return '#9E9E9E'; // Gri
    }
  }

  /// Hedef ikonu (UI için)
  String get targetGoalIcon {
    switch (targetGoal?.toLowerCase()) {
      case 'kilo alma':
        return '📈';
      case 'kilo verme':
        return '📉';
      case 'kas yapma':
        return '💪';
      default:
        return '🎯';
    }
  }
}

/// Antrenman programı listesi response modeli
@JsonSerializable()
class WorkoutProgramListResponse {
  final bool success;
  final String message;
  final List<MemberActiveWorkoutProgramModel> data;

  const WorkoutProgramListResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory WorkoutProgramListResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramListResponseToJson(this);
}

/// Antrenman programı detay response modeli (gelecekte kullanılacak)
@JsonSerializable()
class WorkoutProgramDetailResponse {
  final bool success;
  final String message;
  final MemberActiveWorkoutProgramModel? data;

  const WorkoutProgramDetailResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory WorkoutProgramDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramDetailResponseToJson(this);
}

/// Antrenman programı filtreleme modeli (gelecekte kullanılacak)
@JsonSerializable()
class WorkoutProgramFilter {
  final String? experienceLevel;
  final String? targetGoal;
  final bool? isActive;

  const WorkoutProgramFilter({
    this.experienceLevel,
    this.targetGoal,
    this.isActive,
  });

  factory WorkoutProgramFilter.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramFilterFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramFilterToJson(this);
}

/// Program detay modeli
/// Backend: MemberWorkoutProgramDetailDto
@JsonSerializable()
class MemberWorkoutProgramDetailModel {
  final int memberWorkoutProgramID;
  final int memberID;
  final String memberName;
  final int workoutProgramTemplateID;
  final String programName;
  final String? programDescription;
  final String? experienceLevel;
  final String? targetGoal;
  final DateTime assignedDate;
  final DateTime startDate;
  final DateTime? endDate;
  final String? notes;
  final bool isActive;
  final int dayCount;
  final int exerciseCount;
  final List<WorkoutProgramDayModel> days;

  const MemberWorkoutProgramDetailModel({
    required this.memberWorkoutProgramID,
    required this.memberID,
    required this.memberName,
    required this.workoutProgramTemplateID,
    required this.programName,
    this.programDescription,
    this.experienceLevel,
    this.targetGoal,
    required this.assignedDate,
    required this.startDate,
    this.endDate,
    this.notes,
    required this.isActive,
    required this.dayCount,
    required this.exerciseCount,
    required this.days,
  });

  factory MemberWorkoutProgramDetailModel.fromJson(Map<String, dynamic> json) =>
      _$MemberWorkoutProgramDetailModelFromJson(json);

  Map<String, dynamic> toJson() => _$MemberWorkoutProgramDetailModelToJson(this);

  /// Program aktif mi kontrol et
  bool get isProgramActive {
    final now = DateTime.now();
    if (endDate == null) return true;
    return now.isBefore(endDate!) || now.isAtSameMomentAs(endDate!);
  }

  /// Program kaç gün kaldı
  int? get remainingDays {
    if (endDate == null) return null;
    final now = DateTime.now();
    final difference = endDate!.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  /// Deneyim seviyesi rengi (UI için)
  String get experienceLevelColor {
    switch (experienceLevel?.toLowerCase()) {
      case 'başlangıç':
        return '#4CAF50'; // Yeşil
      case 'orta':
        return '#FF9800'; // Turuncu
      case 'ileri':
        return '#F44336'; // Kırmızı
      default:
        return '#9E9E9E'; // Gri
    }
  }

  /// Hedef ikonu (UI için)
  String get targetGoalIcon {
    switch (targetGoal?.toLowerCase()) {
      case 'kilo alma':
        return '📈';
      case 'kilo verme':
        return '📉';
      case 'kas yapma':
        return '💪';
      default:
        return '🎯';
    }
  }

  /// Toplam egzersiz süresi (dakika)
  int get totalWorkoutDuration {
    int totalMinutes = 0;
    for (var day in days) {
      if (!day.isRestDay) {
        for (var exercise in day.exercises) {
          // Her set için ortalama 1 dakika + dinlenme süresi
          totalMinutes += exercise.sets;
          if (exercise.restTime != null) {
            totalMinutes += (exercise.restTime! * exercise.sets / 60).round();
          }
        }
      }
    }
    return totalMinutes;
  }
}

/// Program günü modeli
/// Backend: WorkoutProgramDayDto
@JsonSerializable()
class WorkoutProgramDayModel {
  final int workoutProgramDayID;
  final int workoutProgramTemplateID;
  final int dayNumber;
  final String dayName;
  final bool isRestDay;
  final DateTime? creationDate;
  final List<WorkoutProgramExerciseModel> exercises;

  const WorkoutProgramDayModel({
    required this.workoutProgramDayID,
    required this.workoutProgramTemplateID,
    required this.dayNumber,
    required this.dayName,
    required this.isRestDay,
    this.creationDate,
    required this.exercises,
  });

  factory WorkoutProgramDayModel.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramDayModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramDayModelToJson(this);

  /// Gün ikonu
  String get dayIcon {
    if (isRestDay) return '😴';

    final lowerDayName = dayName.toLowerCase();
    if (lowerDayName.contains('göğüs')) return '💪';
    if (lowerDayName.contains('sırt')) return '🏋️';
    if (lowerDayName.contains('bacak')) return '🦵';
    if (lowerDayName.contains('omuz')) return '💪';
    if (lowerDayName.contains('kol')) return '💪';
    if (lowerDayName.contains('karın')) return '🔥';

    return '🏃';
  }

  /// Günün toplam egzersiz sayısı
  int get exerciseCount => exercises.length;

  /// Günün toplam set sayısı
  int get totalSets => exercises.fold(0, (sum, exercise) => sum + exercise.sets);
}

/// Program egzersizi modeli
/// Backend: WorkoutProgramExerciseDto
@JsonSerializable()
class WorkoutProgramExerciseModel {
  final int workoutProgramExerciseID;
  final int workoutProgramDayID;
  final String exerciseType;
  final int exerciseID;
  final String exerciseName;
  final String? exerciseDescription;
  final String? categoryName;
  final int orderIndex;
  final int sets;
  final String reps;
  final int? restTime;
  final String? notes;
  final DateTime? creationDate;

  const WorkoutProgramExerciseModel({
    required this.workoutProgramExerciseID,
    required this.workoutProgramDayID,
    required this.exerciseType,
    required this.exerciseID,
    required this.exerciseName,
    this.exerciseDescription,
    this.categoryName,
    required this.orderIndex,
    required this.sets,
    required this.reps,
    this.restTime,
    this.notes,
    this.creationDate,
  });

  factory WorkoutProgramExerciseModel.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramExerciseModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramExerciseModelToJson(this);

  /// Kategori ikonu
  String get categoryIcon {
    final lowerCategory = categoryName?.toLowerCase() ?? '';

    if (lowerCategory.contains('göğüs')) return '💪';
    if (lowerCategory.contains('sırt')) return '🏋️';
    if (lowerCategory.contains('bacak')) return '🦵';
    if (lowerCategory.contains('omuz')) return '💪';
    if (lowerCategory.contains('kol')) return '💪';
    if (lowerCategory.contains('karın')) return '🔥';
    if (lowerCategory.contains('cardio')) return '🏃';

    return '🏋️';
  }

  /// Dinlenme süresi formatı
  String get restTimeFormatted {
    if (restTime == null) return 'Belirtilmemiş';

    if (restTime! < 60) {
      return '${restTime}sn';
    } else {
      final minutes = restTime! ~/ 60;
      final seconds = restTime! % 60;
      if (seconds == 0) {
        return '${minutes}dk';
      } else {
        return '${minutes}dk ${seconds}sn';
      }
    }
  }

  /// Egzersiz tipi formatı
  String get exerciseTypeFormatted {
    switch (exerciseType.toLowerCase()) {
      case 'system':
        return 'Sistem';
      case 'company':
        return 'Özel';
      default:
        return exerciseType;
    }
  }
}

/// Egzersiz ilerleme durumu modeli (PROMPT 5)
/// Backend: MemberExerciseProgressDto
@JsonSerializable()
class MemberExerciseProgressModel {
  final int memberWorkoutProgramID;
  final int currentDayNumber;
  final String currentDayName;
  final bool isCurrentDayCompleted;
  final bool isRestDay;
  final DateTime? lastCompletedDate;
  final List<int> completedExerciseIds;
  final int totalExercisesInDay;
  final int completedExercisesCount;
  final double progressPercentage;
  final bool isProgramCompleted;
  final DateTime? programCompletedDate;
  final int totalDaysInProgram;
  final bool canCompleteDay;

  const MemberExerciseProgressModel({
    required this.memberWorkoutProgramID,
    required this.currentDayNumber,
    required this.currentDayName,
    required this.isCurrentDayCompleted,
    required this.isRestDay,
    this.lastCompletedDate,
    required this.completedExerciseIds,
    required this.totalExercisesInDay,
    required this.completedExercisesCount,
    required this.progressPercentage,
    required this.isProgramCompleted,
    this.programCompletedDate,
    required this.totalDaysInProgram,
    required this.canCompleteDay,
  });

  factory MemberExerciseProgressModel.fromJson(Map<String, dynamic> json) =>
      _$MemberExerciseProgressModelFromJson(json);

  Map<String, dynamic> toJson() => _$MemberExerciseProgressModelToJson(this);

  /// Günün durumu (UI için)
  String get dayStatus {
    if (isProgramCompleted) return 'Program Tamamlandı! 🎉';
    if (isRestDay) return 'Dinlenme Günü 😴';
    if (isCurrentDayCompleted) return 'Gün Tamamlandı ✅';
    return 'Devam Ediyor 💪';
  }

  /// İlerleme durumu rengi
  String get progressColor {
    if (isProgramCompleted) return '#4CAF50'; // Yeşil
    if (progressPercentage >= 80) return '#8BC34A'; // Açık yeşil
    if (progressPercentage >= 50) return '#FF9800'; // Turuncu
    if (progressPercentage >= 25) return '#FFC107'; // Sarı
    return '#F44336'; // Kırmızı
  }

  /// Son aktivite zamanı formatı
  String get lastActivityFormatted {
    if (lastCompletedDate == null) return 'Henüz aktivite yok';

    final now = DateTime.now();
    final difference = now.difference(lastCompletedDate!);

    if (difference.inDays > 0) {
      return '${difference.inDays} gün önce';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} saat önce';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} dakika önce';
    } else {
      return 'Az önce';
    }
  }

  /// Program ilerleme yüzdesi (genel)
  double get overallProgressPercentage {
    if (totalDaysInProgram == 0) return 0;
    return (currentDayNumber / totalDaysInProgram) * 100;
  }

  /// Egzersiz tamamlandı mı kontrol et
  bool isExerciseCompleted(int exerciseId) {
    return completedExerciseIds.contains(exerciseId);
  }
}

/// Egzersiz tamamlama request modeli
@JsonSerializable()
class CompleteExerciseRequest {
  final int memberWorkoutProgramID;
  final int exerciseId;
  final bool isCompleted;

  const CompleteExerciseRequest({
    required this.memberWorkoutProgramID,
    required this.exerciseId,
    required this.isCompleted,
  });

  factory CompleteExerciseRequest.fromJson(Map<String, dynamic> json) =>
      _$CompleteExerciseRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompleteExerciseRequestToJson(this);
}

/// Gün tamamlama request modeli
@JsonSerializable()
class CompleteDayRequest {
  final int memberWorkoutProgramID;

  const CompleteDayRequest({
    required this.memberWorkoutProgramID,
  });

  factory CompleteDayRequest.fromJson(Map<String, dynamic> json) =>
      _$CompleteDayRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompleteDayRequestToJson(this);
}

/// Program sıfırlama request modeli
@JsonSerializable()
class ResetProgramRequest {
  final int memberWorkoutProgramID;

  const ResetProgramRequest({
    required this.memberWorkoutProgramID,
  });

  factory ResetProgramRequest.fromJson(Map<String, dynamic> json) =>
      _$ResetProgramRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ResetProgramRequestToJson(this);
}

/// Program tamamlama onayı request modeli
@JsonSerializable()
class ConfirmProgramCompletionRequest {
  final int memberWorkoutProgramID;

  const ConfirmProgramCompletionRequest({
    required this.memberWorkoutProgramID,
  });

  factory ConfirmProgramCompletionRequest.fromJson(Map<String, dynamic> json) =>
      _$ConfirmProgramCompletionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ConfirmProgramCompletionRequestToJson(this);
}
