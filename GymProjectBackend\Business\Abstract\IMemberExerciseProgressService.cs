using Core.Utilities.Results;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Abstract
{
    public interface IMemberExerciseProgressService
    {
        /// <summary>
        /// Kullanıcının belirli programa ait ilerleme durumunu getirir
        /// </summary>
        IDataResult<MemberExerciseProgressDto> GetProgressByProgram(int userId, int memberWorkoutProgramId);

        /// <summary>
        /// Kullanıcının tüm aktif programlarının ilerleme durumunu getirir
        /// </summary>
        IDataResult<List<MemberExerciseProgressDto>> GetAllProgressByUser(int userId);

        /// <summary>
        /// Egzersiz tamamlama/tamamlamama işlemi (spam korumalı)
        /// </summary>
        IResult CompleteExercise(int userId, CompleteExerciseDto completeDto);

        /// <summary>
        /// Günü tamamlama işlemi
        /// </summary>
        IResult CompleteDay(int userId, CompleteDayDto completeDayDto);

        /// <summary>
        /// Program sıfırlama işlemi
        /// </summary>
        IResult ResetProgram(int userId, ResetProgramDto resetDto);

        /// <summary>
        /// Program tamamlama onayı (program bitince kullanıcı onaylarsa baştan başlar)
        /// </summary>
        IResult ConfirmProgramCompletion(int userId, ConfirmProgramCompletionDto confirmDto);

        /// <summary>
        /// Progress kaydı oluşturur (program atandığında otomatik çağrılır)
        /// </summary>
        IResult CreateProgressRecord(int memberWorkoutProgramId);

        /// <summary>
        /// Progress kaydını siler (program ataması silindiğinde otomatik çağrılır)
        /// </summary>
        IResult DeleteProgressRecord(int memberWorkoutProgramId);
    }
}
