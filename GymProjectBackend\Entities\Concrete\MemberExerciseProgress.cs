using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class MemberExerciseProgress : IEntity
    {
        [Key]
        public int MemberExerciseProgressID { get; set; }
        public int MemberWorkoutProgramID { get; set; }
        public int CurrentDayNumber { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public bool IsCurrentDayCompleted { get; set; }
        public string? CompletedExercises { get; set; } // JSON array: [1,3,5]
        public bool IsProgramCompleted { get; set; }
        public DateTime? ProgramCompletedDate { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
