/// Exercise Progress API Service - GymKod Pro Mobile
///
/// Bu service egzersiz ilerleme takip API'leri ile iletişim kurar.
/// Backend: MemberExerciseProgressController
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';

/// Exercise Progress API Service Interface
abstract class ExerciseProgressApiService {
  /// Kullanıcının belirli programa ait ilerleme durumunu al
  /// Backend: GetProgressByProgram
  Future<ApiResponse<MemberExerciseProgressModel>> getProgressByProgram(int memberWorkoutProgramId);

  /// Kullanıcının tüm aktif programlarının ilerleme durumunu al
  /// Backend: GetAllProgressByUser
  Future<ApiResponse<List<MemberExerciseProgressModel>>> getAllProgressByUser();

  /// Egzersiz tamamlama/tamamlamama işlemi
  /// Backend: CompleteExercise
  Future<ApiResponse<bool>> completeExercise(CompleteExerciseRequest request);

  /// Günü tamamlama işlemi
  /// Backend: CompleteDay
  Future<ApiResponse<bool>> completeDay(CompleteDayRequest request);

  /// Program sıfırlama işlemi
  /// Backend: ResetProgram
  Future<ApiResponse<bool>> resetProgram(ResetProgramRequest request);

  /// Program tamamlama onayı
  /// Backend: ConfirmProgramCompletion
  Future<ApiResponse<bool>> confirmProgramCompletion(ConfirmProgramCompletionRequest request);
}

/// Exercise Progress API Service Implementation
class ExerciseProgressApiServiceImpl implements ExerciseProgressApiService {
  final ApiService _apiService;

  ExerciseProgressApiServiceImpl(this._apiService);

  @override
  Future<ApiResponse<MemberExerciseProgressModel>> getProgressByProgram(int memberWorkoutProgramId) async {
    try {
      LoggingService.apiRequest('GET', 'MemberExerciseProgress/getprogresspyprogram');

      // Token'dan user ID'yi al
      final userModel = await TokenService.getUserFromToken();
      if (userModel == null) {
        LoggingService.authLog('User token not found');
        return ApiResponse.error(message: 'Oturum bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.get(
        'MemberExerciseProgress/getprogresspyprogram',
        queryParameters: {
          'userId': userId,
          'memberWorkoutProgramId': memberWorkoutProgramId,
        },
      );

      LoggingService.apiResponse(
        'GET',
        'MemberExerciseProgress/getprogresspyprogram',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final progressData = responseData['data'] as Map<String, dynamic>;
          final progress = MemberExerciseProgressModel.fromJson(progressData);

          LoggingService.stateLog(
            'ExerciseProgressAPI',
            'Progress loaded successfully',
            state: 'Day: ${progress.currentDayNumber}, Progress: ${progress.progressPercentage}%',
          );

          return ApiResponse.success(
            data: progress,
            message: responseData['message'] as String? ?? 'İlerleme durumu yüklendi',
          );
        } else {
          final errorMessage = responseData['message'] as String? ?? 'İlerleme durumu yüklenemedi';
          LoggingService.apiError('Progress load failed', errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        final errorMessage = 'Sunucu hatası: ${response.statusCode}';
        LoggingService.apiError('Progress API error', errorMessage);
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressApiService.getProgressByProgram',
      );

      return ApiResponse.error(message: 'İlerleme durumu yüklenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<List<MemberExerciseProgressModel>>> getAllProgressByUser() async {
    try {
      LoggingService.apiRequest('GET', 'MemberExerciseProgress/getallprogressbyuser');

      // Token'dan user ID'yi al
      final userModel = await TokenService.getUserFromToken();
      if (userModel == null) {
        LoggingService.authLog('User token not found');
        return ApiResponse.error(message: 'Oturum bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.get(
        'MemberExerciseProgress/getallprogressbyuser',
        queryParameters: {
          'userId': userId,
        },
      );

      LoggingService.apiResponse(
        'GET',
        'MemberExerciseProgress/getallprogressbyuser',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['data'] != null) {
          final progressListData = responseData['data'] as List<dynamic>;
          final progressList = progressListData
              .map((item) => MemberExerciseProgressModel.fromJson(item as Map<String, dynamic>))
              .toList();

          LoggingService.stateLog(
            'ExerciseProgressAPI',
            'All progress loaded successfully',
            state: 'Count: ${progressList.length}',
          );

          return ApiResponse.success(
            data: progressList,
            message: responseData['message'] as String? ?? 'Tüm ilerleme durumları yüklendi',
          );
        } else {
          final errorMessage = responseData['message'] as String? ?? 'İlerleme durumları yüklenemedi';
          LoggingService.apiError('All progress load failed', errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        final errorMessage = 'Sunucu hatası: ${response.statusCode}';
        LoggingService.apiError('All progress API error', errorMessage);
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressApiService.getAllProgressByUser',
      );

      return ApiResponse.error(message: 'İlerleme durumları yüklenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> completeExercise(CompleteExerciseRequest request) async {
    try {
      LoggingService.apiRequest('POST', 'MemberExerciseProgress/completeexercise');

      // Token'dan user ID'yi al
      final userModel = await TokenService.getUserFromToken();
      if (userModel == null) {
        LoggingService.authLog('User token not found');
        return ApiResponse.error(message: 'Oturum bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.post(
        'MemberExerciseProgress/completeexercise',
        data: request.toJson(),
        queryParameters: {
          'userId': userId,
        },
      );

      LoggingService.apiResponse(
        'POST',
        'MemberExerciseProgress/completeexercise',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          LoggingService.stateLog(
            'ExerciseProgressAPI',
            'Exercise completion updated',
            state: 'Exercise: ${request.exerciseId}, Completed: ${request.isCompleted}',
          );

          return ApiResponse.success(
            data: true,
            message: responseData['message'] as String? ?? 'Egzersiz durumu güncellendi',
          );
        } else {
          final errorMessage = responseData['message'] as String? ?? 'Egzersiz durumu güncellenemedi';
          LoggingService.apiError('Exercise completion failed', errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        final errorMessage = 'Sunucu hatası: ${response.statusCode}';
        LoggingService.apiError('Exercise completion API error', errorMessage);
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressApiService.completeExercise',
      );

      return ApiResponse.error(message: 'Egzersiz durumu güncellenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> completeDay(CompleteDayRequest request) async {
    try {
      LoggingService.apiRequest('POST', 'MemberExerciseProgress/completeday');

      // Token'dan user ID'yi al
      final userModel = await TokenService.getUserFromToken();
      if (userModel == null) {
        LoggingService.authLog('User token not found');
        return ApiResponse.error(message: 'Oturum bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.post(
        'MemberExerciseProgress/completeday',
        data: request.toJson(),
        queryParameters: {
          'userId': userId,
        },
      );

      LoggingService.apiResponse(
        'POST',
        'MemberExerciseProgress/completeday',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          LoggingService.stateLog(
            'ExerciseProgressAPI',
            'Day completed successfully',
            state: 'Program: ${request.memberWorkoutProgramID}',
          );

          return ApiResponse.success(
            data: true,
            message: responseData['message'] as String? ?? 'Gün başarıyla tamamlandı',
          );
        } else {
          final errorMessage = responseData['message'] as String? ?? 'Gün tamamlanamadı';
          LoggingService.apiError('Day completion failed', errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        final errorMessage = 'Sunucu hatası: ${response.statusCode}';
        LoggingService.apiError('Day completion API error', errorMessage);
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressApiService.completeDay',
      );

      return ApiResponse.error(message: 'Gün tamamlanırken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> resetProgram(ResetProgramRequest request) async {
    try {
      LoggingService.apiRequest('POST', 'MemberExerciseProgress/resetprogram');

      // Token'dan user ID'yi al
      final userModel = await TokenService.getUserFromToken();
      if (userModel == null) {
        LoggingService.authLog('User token not found');
        return ApiResponse.error(message: 'Oturum bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.post(
        'MemberExerciseProgress/resetprogram',
        data: request.toJson(),
        queryParameters: {
          'userId': userId,
        },
      );

      LoggingService.apiResponse(
        'POST',
        'MemberExerciseProgress/resetprogram',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          LoggingService.stateLog(
            'ExerciseProgressAPI',
            'Program reset successfully',
            state: 'Program: ${request.memberWorkoutProgramID}',
          );

          return ApiResponse.success(
            data: true,
            message: responseData['message'] as String? ?? 'Program başarıyla sıfırlandı',
          );
        } else {
          final errorMessage = responseData['message'] as String? ?? 'Program sıfırlanamadı';
          LoggingService.apiError('Program reset failed', errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        final errorMessage = 'Sunucu hatası: ${response.statusCode}';
        LoggingService.apiError('Program reset API error', errorMessage);
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressApiService.resetProgram',
      );

      return ApiResponse.error(message: 'Program sıfırlanırken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> confirmProgramCompletion(ConfirmProgramCompletionRequest request) async {
    try {
      LoggingService.apiRequest('POST', 'MemberExerciseProgress/confirmprogramcompletion');

      // Token'dan user ID'yi al
      final userModel = await TokenService.getUserFromToken();
      if (userModel == null) {
        LoggingService.authLog('User token not found');
        return ApiResponse.error(message: 'Oturum bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı
      final response = await _apiService.post(
        'MemberExerciseProgress/confirmprogramcompletion',
        data: request.toJson(),
        queryParameters: {
          'userId': userId,
        },
      );

      LoggingService.apiResponse(
        'POST',
        'MemberExerciseProgress/confirmprogramcompletion',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          LoggingService.stateLog(
            'ExerciseProgressAPI',
            'Program completion confirmed',
            state: 'Program: ${request.memberWorkoutProgramID}',
          );

          return ApiResponse.success(
            data: true,
            message: responseData['message'] as String? ?? 'Program yeniden başlatıldı',
          );
        } else {
          final errorMessage = responseData['message'] as String? ?? 'Program onaylanamadı';
          LoggingService.apiError('Program confirmation failed', errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        final errorMessage = 'Sunucu hatası: ${response.statusCode}';
        LoggingService.apiError('Program confirmation API error', errorMessage);
        return ApiResponse.error(message: errorMessage);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressApiService.confirmProgramCompletion',
      );

      return ApiResponse.error(message: 'Program onaylanırken hata oluştu: ${e.toString()}');
    }
  }
}

/// Exercise Progress Repository Interface
abstract class ExerciseProgressRepository {
  Future<ApiResponse<MemberExerciseProgressModel>> getProgressByProgram(int memberWorkoutProgramId);
  Future<ApiResponse<List<MemberExerciseProgressModel>>> getAllProgressByUser();
  Future<ApiResponse<bool>> completeExercise(CompleteExerciseRequest request);
  Future<ApiResponse<bool>> completeDay(CompleteDayRequest request);
  Future<ApiResponse<bool>> resetProgram(ResetProgramRequest request);
  Future<ApiResponse<bool>> confirmProgramCompletion(ConfirmProgramCompletionRequest request);
}

/// Exercise Progress Repository Implementation
class ExerciseProgressRepositoryImpl implements ExerciseProgressRepository {
  final ExerciseProgressApiService _apiService;

  ExerciseProgressRepositoryImpl(this._apiService);

  @override
  Future<ApiResponse<MemberExerciseProgressModel>> getProgressByProgram(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('ExerciseProgressRepository', 'Loading progress by program');

      final result = await _apiService.getProgressByProgram(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Progress loaded successfully',
          state: 'Day: ${result.data?.currentDayNumber}',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Failed to load progress',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressRepository.getProgressByProgram',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<List<MemberExerciseProgressModel>>> getAllProgressByUser() async {
    try {
      LoggingService.stateLog('ExerciseProgressRepository', 'Loading all progress by user');

      final result = await _apiService.getAllProgressByUser();

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'All progress loaded successfully',
          state: 'Count: ${result.data?.length ?? 0}',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Failed to load all progress',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressRepository.getAllProgressByUser',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> completeExercise(CompleteExerciseRequest request) async {
    try {
      LoggingService.stateLog('ExerciseProgressRepository', 'Completing exercise', state: 'Exercise: ${request.exerciseId}');

      final result = await _apiService.completeExercise(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Exercise completed successfully',
          state: 'Exercise: ${request.exerciseId}, Completed: ${request.isCompleted}',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Failed to complete exercise',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressRepository.completeExercise',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> completeDay(CompleteDayRequest request) async {
    try {
      LoggingService.stateLog('ExerciseProgressRepository', 'Completing day', state: 'Program: ${request.memberWorkoutProgramID}');

      final result = await _apiService.completeDay(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Day completed successfully',
          state: 'Program: ${request.memberWorkoutProgramID}',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Failed to complete day',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressRepository.completeDay',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> resetProgram(ResetProgramRequest request) async {
    try {
      LoggingService.stateLog('ExerciseProgressRepository', 'Resetting program', state: 'Program: ${request.memberWorkoutProgramID}');

      final result = await _apiService.resetProgram(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Program reset successfully',
          state: 'Program: ${request.memberWorkoutProgramID}',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Failed to reset program',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressRepository.resetProgram',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<bool>> confirmProgramCompletion(ConfirmProgramCompletionRequest request) async {
    try {
      LoggingService.stateLog('ExerciseProgressRepository', 'Confirming program completion', state: 'Program: ${request.memberWorkoutProgramID}');

      final result = await _apiService.confirmProgramCompletion(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Program completion confirmed successfully',
          state: 'Program: ${request.memberWorkoutProgramID}',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgressRepository',
          'Failed to confirm program completion',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressRepository.confirmProgramCompletion',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }
}

/// Providers
final exerciseProgressApiServiceProvider = Provider<ExerciseProgressApiService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return ExerciseProgressApiServiceImpl(apiService);
});

final exerciseProgressRepositoryProvider = Provider<ExerciseProgressRepository>((ref) {
  final apiService = ref.read(exerciseProgressApiServiceProvider);
  return ExerciseProgressRepositoryImpl(apiService);
});
