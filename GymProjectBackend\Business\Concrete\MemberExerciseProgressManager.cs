using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.DTOs;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace Business.Concrete
{
    public class MemberExerciseProgressManager : IMemberExerciseProgressService
    {
        private readonly IMemberExerciseProgressDal _memberExerciseProgressDal;
        private readonly IMemberWorkoutProgramDal _memberWorkoutProgramDal;
        private readonly IWorkoutProgramDayDal _workoutProgramDayDal;
        private readonly IWorkoutProgramExerciseDal _workoutProgramExerciseDal;
        private readonly IMemoryCache _cache;

        public MemberExerciseProgressManager(
            IMemberExerciseProgressDal memberExerciseProgressDal,
            IMemberWorkoutProgramDal memberWorkoutProgramDal,
            IWorkoutProgramDayDal workoutProgramDayDal,
            IWorkoutProgramExerciseDal workoutProgramExerciseDal,
            IMemoryCache cache)
        {
            _memberExerciseProgressDal = memberExerciseProgressDal;
            _memberWorkoutProgramDal = memberWorkoutProgramDal;
            _workoutProgramDayDal = workoutProgramDayDal;
            _workoutProgramExerciseDal = workoutProgramExerciseDal;
            _cache = cache;
        }

        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 2, "ExerciseProgress", "UserProgress")]
        public IDataResult<MemberExerciseProgressDto> GetProgressByProgram(int userId, int memberWorkoutProgramId)
        {
            var result = _memberExerciseProgressDal.GetProgressByProgram(userId, memberWorkoutProgramId);
            if (result == null)
            {
                return new ErrorDataResult<MemberExerciseProgressDto>("İlerleme kaydı bulunamadı.");
            }
            return new SuccessDataResult<MemberExerciseProgressDto>(result);
        }

        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 2, "ExerciseProgress", "AllUserProgress")]
        public IDataResult<List<MemberExerciseProgressDto>> GetAllProgressByUser(int userId)
        {
            var result = _memberExerciseProgressDal.GetAllProgressByUser(userId);
            return new SuccessDataResult<List<MemberExerciseProgressDto>>(result);
        }

        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("ExerciseProgress")]
        [PerformanceAspect(3)]
        public IResult CompleteExercise(int userId, CompleteExerciseDto completeDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfUserHasAccessToProgram(userId, completeDto.MemberWorkoutProgramID),
                CheckSpamPrevention(userId, completeDto.ExerciseId),
                CheckIfProgramIsNotCompleted(userId, completeDto.MemberWorkoutProgramID)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var progressEntity = _memberExerciseProgressDal.GetProgressEntityByProgram(userId, completeDto.MemberWorkoutProgramID);
            if (progressEntity == null)
            {
                return new ErrorResult("İlerleme kaydı bulunamadı.");
            }

            // Tamamlanan egzersizleri parse et
            var completedExercises = new List<int>();
            if (!string.IsNullOrEmpty(progressEntity.CompletedExercises))
            {
                try
                {
                    completedExercises = JsonSerializer.Deserialize<List<int>>(progressEntity.CompletedExercises) ?? new List<int>();
                }
                catch
                {
                    completedExercises = new List<int>();
                }
            }

            // Egzersiz durumunu güncelle
            if (completeDto.IsCompleted)
            {
                if (!completedExercises.Contains(completeDto.ExerciseId))
                {
                    completedExercises.Add(completeDto.ExerciseId);
                }
            }
            else
            {
                completedExercises.Remove(completeDto.ExerciseId);
            }

            // JSON'a çevir ve kaydet
            progressEntity.CompletedExercises = JsonSerializer.Serialize(completedExercises);
            progressEntity.LastCompletedDate = DateTime.Now;
            progressEntity.UpdatedDate = DateTime.Now;

            _memberExerciseProgressDal.Update(progressEntity);

            // Spam önleme cache'i güncelle
            var cacheKey = $"exercise_action_{userId}_{completeDto.ExerciseId}";
            _cache.Set(cacheKey, DateTime.Now, TimeSpan.FromSeconds(2));

            return new SuccessResult("Egzersiz durumu güncellendi.");
        }

        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("ExerciseProgress")]
        [PerformanceAspect(3)]
        public IResult CompleteDay(int userId, CompleteDayDto completeDayDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfUserHasAccessToProgram(userId, completeDayDto.MemberWorkoutProgramID),
                CheckIfAllExercisesCompleted(userId, completeDayDto.MemberWorkoutProgramID),
                CheckIfProgramIsNotCompleted(userId, completeDayDto.MemberWorkoutProgramID)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var progressEntity = _memberExerciseProgressDal.GetProgressEntityByProgram(userId, completeDayDto.MemberWorkoutProgramID);
            if (progressEntity == null)
            {
                return new ErrorResult("İlerleme kaydı bulunamadı.");
            }

            // Günü tamamlandı olarak işaretle
            progressEntity.IsCurrentDayCompleted = true;
            progressEntity.LastCompletedDate = DateTime.Now;
            progressEntity.UpdatedDate = DateTime.Now;

            // Sonraki güne geç
            var nextDayResult = MoveToNextDay(progressEntity, completeDayDto.MemberWorkoutProgramID);
            if (!nextDayResult.Success)
            {
                return nextDayResult;
            }

            _memberExerciseProgressDal.Update(progressEntity);

            return new SuccessResult("Gün başarıyla tamamlandı!");
        }

        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("ExerciseProgress")]
        [PerformanceAspect(3)]
        public IResult ResetProgram(int userId, ResetProgramDto resetDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfUserHasAccessToProgram(userId, resetDto.MemberWorkoutProgramID)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var progressEntity = _memberExerciseProgressDal.GetProgressEntityByProgram(userId, resetDto.MemberWorkoutProgramID);
            if (progressEntity == null)
            {
                return new ErrorResult("İlerleme kaydı bulunamadı.");
            }

            // Progress'i sıfırla
            progressEntity.CurrentDayNumber = 1;
            progressEntity.IsCurrentDayCompleted = false;
            progressEntity.CompletedExercises = "[]";
            progressEntity.IsProgramCompleted = false;
            progressEntity.ProgramCompletedDate = null;
            progressEntity.UpdatedDate = DateTime.Now;

            _memberExerciseProgressDal.Update(progressEntity);

            return new SuccessResult("Program başarıyla sıfırlandı!");
        }

        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("ExerciseProgress")]
        [PerformanceAspect(3)]
        public IResult ConfirmProgramCompletion(int userId, ConfirmProgramCompletionDto confirmDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfUserHasAccessToProgram(userId, confirmDto.MemberWorkoutProgramID),
                CheckIfProgramIsCompleted(userId, confirmDto.MemberWorkoutProgramID)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            // Program tamamlama onaylandı, baştan başlat
            var resetDto = new ResetProgramDto { MemberWorkoutProgramID = confirmDto.MemberWorkoutProgramID };
            return ResetProgram(userId, resetDto);
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult CreateProgressRecord(int memberWorkoutProgramId)
        {
            try
            {
                _memberExerciseProgressDal.CreateProgressRecord(memberWorkoutProgramId);
                return new SuccessResult("İlerleme kaydı oluşturuldu.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"İlerleme kaydı oluşturulurken hata: {ex.Message}");
            }
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult DeleteProgressRecord(int memberWorkoutProgramId)
        {
            try
            {
                _memberExerciseProgressDal.DeleteProgressRecord(memberWorkoutProgramId);
                return new SuccessResult("İlerleme kaydı silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"İlerleme kaydı silinirken hata: {ex.Message}");
            }
        }

        // Helper Methods
        private IResult MoveToNextDay(Entities.Concrete.MemberExerciseProgress progressEntity, int memberWorkoutProgramId)
        {
            // Program şablonunu al
            var assignment = _memberWorkoutProgramDal.Get(mwp => mwp.MemberWorkoutProgramID == memberWorkoutProgramId);
            if (assignment == null)
            {
                return new ErrorResult("Program ataması bulunamadı.");
            }

            // Toplam gün sayısını al
            var totalDays = _workoutProgramDayDal.GetAll(wpd => wpd.WorkoutProgramTemplateID == assignment.WorkoutProgramTemplateID).Count;

            if (progressEntity.CurrentDayNumber >= totalDays)
            {
                // Program tamamlandı
                progressEntity.IsProgramCompleted = true;
                progressEntity.ProgramCompletedDate = DateTime.Now;
                progressEntity.CurrentDayNumber = totalDays; // Son günde kal
                progressEntity.CompletedExercises = "[]";
                return new SuccessResult("Program tamamlandı!");
            }
            else
            {
                // Sonraki güne geç
                progressEntity.CurrentDayNumber++;
                progressEntity.IsCurrentDayCompleted = false;
                progressEntity.CompletedExercises = "[]"; // Yeni gün için sıfırla
                return new SuccessResult("Sonraki güne geçildi.");
            }
        }

        // Business Rules
        private IResult CheckIfUserHasAccessToProgram(int userId, int memberWorkoutProgramId)
        {
            var progressEntity = _memberExerciseProgressDal.GetProgressEntityByProgram(userId, memberWorkoutProgramId);
            if (progressEntity == null)
            {
                return new ErrorResult("Bu programa erişim yetkiniz yok.");
            }
            return new SuccessResult();
        }

        private IResult CheckSpamPrevention(int userId, int exerciseId)
        {
            var cacheKey = $"exercise_action_{userId}_{exerciseId}";
            if (_cache.TryGetValue(cacheKey, out DateTime lastAction))
            {
                var timeDifference = DateTime.Now.Subtract(lastAction);
                if (timeDifference.TotalSeconds < 2) // 2 saniye bekleme
                {
                    return new ErrorResult("Çok hızlı işlem yapıyorsunuz. Lütfen bekleyin.");
                }
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramIsNotCompleted(int userId, int memberWorkoutProgramId)
        {
            var progressEntity = _memberExerciseProgressDal.GetProgressEntityByProgram(userId, memberWorkoutProgramId);
            if (progressEntity != null && progressEntity.IsProgramCompleted)
            {
                return new ErrorResult("Bu program zaten tamamlanmış. Yeni bir döngü başlatmak için onaylayın.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramIsCompleted(int userId, int memberWorkoutProgramId)
        {
            var progressEntity = _memberExerciseProgressDal.GetProgressEntityByProgram(userId, memberWorkoutProgramId);
            if (progressEntity == null || !progressEntity.IsProgramCompleted)
            {
                return new ErrorResult("Bu program henüz tamamlanmamış.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfAllExercisesCompleted(int userId, int memberWorkoutProgramId)
        {
            var progressDto = _memberExerciseProgressDal.GetProgressByProgram(userId, memberWorkoutProgramId);
            if (progressDto == null)
            {
                return new ErrorResult("İlerleme kaydı bulunamadı.");
            }

            if (progressDto.IsRestDay)
            {
                return new SuccessResult(); // Dinlenme günü, egzersiz kontrolü yok
            }

            if (progressDto.CompletedExercisesCount < progressDto.TotalExercisesInDay)
            {
                return new ErrorResult($"Günü tamamlamak için tüm egzersizleri yapmalısınız. ({progressDto.CompletedExercisesCount}/{progressDto.TotalExercisesInDay})");
            }

            return new SuccessResult();
        }
    }
}
