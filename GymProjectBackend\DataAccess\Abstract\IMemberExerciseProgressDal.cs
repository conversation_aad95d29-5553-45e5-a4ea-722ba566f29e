using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    public interface IMemberExerciseProgressDal : IEntityRepository<MemberExerciseProgress>
    {
        /// <summary>
        /// Kullanıcının belirli programa ait ilerleme durumunu getirir
        /// </summary>
        MemberExerciseProgressDto GetProgressByProgram(int userId, int memberWorkoutProgramId);

        /// <summary>
        /// Kullanıcının tüm aktif programlarının ilerleme durumunu getirir
        /// </summary>
        List<MemberExerciseProgressDto> GetAllProgressByUser(int userId);

        /// <summary>
        /// Kullanıcının belirli programa ait progress kaydını getirir (entity olarak)
        /// </summary>
        MemberExerciseProgress GetProgressEntityByProgram(int userId, int memberWorkoutProgramId);

        /// <summary>
        /// Progress kaydı oluşturur (program atandığında)
        /// </summary>
        void CreateProgressRecord(int memberWorkoutProgramId);

        /// <summary>
        /// Progress kaydını siler (program ataması silindiğinde)
        /// </summary>
        void DeleteProgressRecord(int memberWorkoutProgramId);
    }
}
