using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MemberExerciseProgressController : ControllerBase
    {
        private readonly IMemberExerciseProgressService _memberExerciseProgressService;

        public MemberExerciseProgressController(IMemberExerciseProgressService memberExerciseProgressService)
        {
            _memberExerciseProgressService = memberExerciseProgressService;
        }

        /// <summary>
        /// Kullanıcının belirli programa ait ilerleme durumunu getirir (mobil API)
        /// </summary>
        [HttpGet("getprogresspyprogram")]
        public IActionResult GetProgressByProgram(int userId, int memberWorkoutProgramId)
        {
            var result = _memberExerciseProgressService.GetProgressByProgram(userId, memberWorkoutProgramId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kullanıcının tüm aktif programlarının ilerleme durumunu getirir (mobil API)
        /// </summary>
        [HttpGet("getallprogressbyuser")]
        public IActionResult GetAllProgressByUser(int userId)
        {
            var result = _memberExerciseProgressService.GetAllProgressByUser(userId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Egzersiz tamamlama/tamamlamama işlemi (mobil API)
        /// </summary>
        [HttpPost("completeexercise")]
        public IActionResult CompleteExercise([FromBody] CompleteExerciseDto completeDto, [FromQuery] int userId)
        {
            var result = _memberExerciseProgressService.CompleteExercise(userId, completeDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Günü tamamlama işlemi (mobil API)
        /// </summary>
        [HttpPost("completeday")]
        public IActionResult CompleteDay([FromBody] CompleteDayDto completeDayDto, [FromQuery] int userId)
        {
            var result = _memberExerciseProgressService.CompleteDay(userId, completeDayDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Program sıfırlama işlemi (mobil API)
        /// </summary>
        [HttpPost("resetprogram")]
        public IActionResult ResetProgram([FromBody] ResetProgramDto resetDto, [FromQuery] int userId)
        {
            var result = _memberExerciseProgressService.ResetProgram(userId, resetDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Program tamamlama onayı (mobil API)
        /// </summary>
        [HttpPost("confirmprogramcompletion")]
        public IActionResult ConfirmProgramCompletion([FromBody] ConfirmProgramCompletionDto confirmDto, [FromQuery] int userId)
        {
            var result = _memberExerciseProgressService.ConfirmProgramCompletion(userId, confirmDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
