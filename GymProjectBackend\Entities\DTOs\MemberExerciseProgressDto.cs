using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Üye egzersiz ilerleme durumu DTO'su (mobil API için)
    /// </summary>
    public class MemberExerciseProgressDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int CurrentDayNumber { get; set; }
        public string CurrentDayName { get; set; }
        public bool IsCurrentDayCompleted { get; set; }
        public bool IsRestDay { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public List<int> CompletedExerciseIds { get; set; } = new List<int>();
        public int TotalExercisesInDay { get; set; }
        public int CompletedExercisesCount { get; set; }
        public double ProgressPercentage { get; set; }
        public bool IsProgramCompleted { get; set; }
        public DateTime? ProgramCompletedDate { get; set; }
        public int TotalDaysInProgram { get; set; }
        public bool CanCompleteDay { get; set; } // Tüm egzersizler tamamlandı mı?
    }

    /// <summary>
    /// Egzersiz tamamlama DTO'su
    /// </summary>
    public class CompleteExerciseDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int ExerciseId { get; set; }
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// Günü tamamlama DTO'su
    /// </summary>
    public class CompleteDayDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
    }

    /// <summary>
    /// Program sıfırlama DTO'su
    /// </summary>
    public class ResetProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
    }

    /// <summary>
    /// Program tamamlama onayı DTO'su
    /// </summary>
    public class ConfirmProgramCompletionDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
    }
}
