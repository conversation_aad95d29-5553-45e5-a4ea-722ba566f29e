/// Exercise Progress Provider - GymKod Pro Mobile
///
/// Bu provider egzersiz ilerleme takip state management'ı sağlar.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../data/services/exercise_progress_api_service.dart';

/// Exercise Progress State
class ExerciseProgressState {
  final Map<int, MemberExerciseProgressModel> progressByProgram;
  final bool isLoading;
  final bool isRefreshing;
  final bool isUpdating;
  final String? error;
  final String? successMessage;
  final DateTime? lastUpdated;
  final Map<int, DateTime> lastExerciseToggle; // Spam önleme için

  const ExerciseProgressState({
    this.progressByProgram = const {},
    this.isLoading = false,
    this.isRefreshing = false,
    this.isUpdating = false,
    this.error,
    this.successMessage,
    this.lastUpdated,
    this.lastExerciseToggle = const {},
  });

  ExerciseProgressState copyWith({
    Map<int, MemberExerciseProgressModel>? progressByProgram,
    bool? isLoading,
    bool? isRefreshing,
    bool? isUpdating,
    String? error,
    String? successMessage,
    DateTime? lastUpdated,
    Map<int, DateTime>? lastExerciseToggle,
    bool clearError = false,
    bool clearSuccessMessage = false,
  }) {
    return ExerciseProgressState(
      progressByProgram: progressByProgram ?? this.progressByProgram,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isUpdating: isUpdating ?? this.isUpdating,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
      lastExerciseToggle: lastExerciseToggle ?? this.lastExerciseToggle,
    );
  }

  /// Belirli programa ait progress var mı?
  bool hasProgressForProgram(int memberWorkoutProgramId) {
    return progressByProgram.containsKey(memberWorkoutProgramId);
  }

  /// Belirli programa ait progress'i al
  MemberExerciseProgressModel? getProgressForProgram(int memberWorkoutProgramId) {
    return progressByProgram[memberWorkoutProgramId];
  }

  /// Herhangi bir progress var mı?
  bool get hasAnyProgress => progressByProgram.isNotEmpty;

  /// Tamamlanan program sayısı
  int get completedProgramCount {
    return progressByProgram.values.where((progress) => progress.isProgramCompleted).length;
  }

  /// Aktif program sayısı
  int get activeProgramCount {
    return progressByProgram.values.where((progress) => !progress.isProgramCompleted).length;
  }
}

/// Exercise Progress Notifier
class ExerciseProgressNotifier extends StateNotifier<ExerciseProgressState> {
  final ExerciseProgressRepository _repository;

  ExerciseProgressNotifier(this._repository) : super(const ExerciseProgressState());

  /// Belirli programa ait progress'i yükle
  Future<void> loadProgressByProgram(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Loading progress by program', state: 'ID: $memberWorkoutProgramId');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getProgressByProgram(memberWorkoutProgramId);

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Progress loaded successfully',
          state: 'Day: ${result.data!.currentDayNumber}, Progress: ${result.data!.progressPercentage}%',
        );

        final updatedProgress = Map<int, MemberExerciseProgressModel>.from(state.progressByProgram);
        updatedProgress[memberWorkoutProgramId] = result.data!;

        state = state.copyWith(
          progressByProgram: updatedProgress,
          isLoading: false,
          lastUpdated: DateTime.now(),
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Failed to load progress',
          state: result.message,
        );

        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.loadProgressByProgram',
      );

      state = state.copyWith(
        isLoading: false,
        error: 'İlerleme durumu yüklenirken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Tüm progress'leri yükle
  Future<void> loadAllProgress() async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Loading all progress');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getAllProgressByUser();

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'All progress loaded successfully',
          state: 'Count: ${result.data!.length}',
        );

        final progressMap = <int, MemberExerciseProgressModel>{};
        for (final progress in result.data!) {
          progressMap[progress.memberWorkoutProgramID] = progress;
        }

        state = state.copyWith(
          progressByProgram: progressMap,
          isLoading: false,
          lastUpdated: DateTime.now(),
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Failed to load all progress',
          state: result.message,
        );

        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.loadAllProgress',
      );

      state = state.copyWith(
        isLoading: false,
        error: 'İlerleme durumları yüklenirken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Progress'leri yenile
  Future<void> refreshProgress() async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Refreshing progress');

      state = state.copyWith(
        isRefreshing: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getAllProgressByUser();

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Progress refreshed successfully',
          state: 'Count: ${result.data!.length}',
        );

        final progressMap = <int, MemberExerciseProgressModel>{};
        for (final progress in result.data!) {
          progressMap[progress.memberWorkoutProgramID] = progress;
        }

        state = state.copyWith(
          progressByProgram: progressMap,
          isRefreshing: false,
          lastUpdated: DateTime.now(),
          successMessage: 'İlerleme durumları güncellendi',
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Failed to refresh progress',
          state: result.message,
        );

        state = state.copyWith(
          isRefreshing: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.refreshProgress',
      );

      state = state.copyWith(
        isRefreshing: false,
        error: 'İlerleme durumları yenilenirken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Egzersiz tamamlama/tamamlamama - Optimistic UI
  Future<void> toggleExercise(int memberWorkoutProgramId, int exerciseId, bool isCompleted) async {
    // Hafif spam önleme kontrolü (sadece çok hızlı tıklamaları engelle)
    if (!_canToggleExercise(exerciseId)) {
      state = state.copyWith(
        error: 'Çok hızlı işlem yapıyorsunuz. Lütfen bekleyin.',
      );
      return;
    }

    // Mevcut progress'i al
    final currentProgress = state.progressByProgram[memberWorkoutProgramId];
    if (currentProgress == null) {
      state = state.copyWith(
        error: 'İlerleme bilgisi bulunamadı.',
      );
      return;
    }

    LoggingService.stateLog(
      'ExerciseProgress',
      'Optimistic UI: Toggling exercise',
      state: 'Exercise: $exerciseId, Completed: $isCompleted',
    );

    // OPTIMISTIC UI: Önce UI'ı güncelle
    final updatedCompletedExercises = List<int>.from(currentProgress.completedExerciseIds);

    if (isCompleted) {
      // Egzersizi tamamlanmış listesine ekle
      if (!updatedCompletedExercises.contains(exerciseId)) {
        updatedCompletedExercises.add(exerciseId);
      }
    } else {
      // Egzersizi tamamlanmış listesinden çıkar
      updatedCompletedExercises.remove(exerciseId);
    }

    // Yeni progress objesi oluştur (manuel olarak)
    final updatedProgress = MemberExerciseProgressModel(
      memberWorkoutProgramID: currentProgress.memberWorkoutProgramID,
      currentDayNumber: currentProgress.currentDayNumber,
      currentDayName: currentProgress.currentDayName,
      isCurrentDayCompleted: currentProgress.isCurrentDayCompleted,
      isRestDay: currentProgress.isRestDay,
      lastCompletedDate: currentProgress.lastCompletedDate,
      completedExerciseIds: updatedCompletedExercises,
      totalExercisesInDay: currentProgress.totalExercisesInDay,
      completedExercisesCount: updatedCompletedExercises.length,
      progressPercentage: currentProgress.totalExercisesInDay > 0
          ? (updatedCompletedExercises.length / currentProgress.totalExercisesInDay) * 100
          : 0,
      isProgramCompleted: currentProgress.isProgramCompleted,
      programCompletedDate: currentProgress.programCompletedDate,
      totalDaysInProgram: currentProgress.totalDaysInProgram,
      canCompleteDay: !currentProgress.isRestDay && updatedCompletedExercises.length == currentProgress.totalExercisesInDay,
    );

    // UI'ı hemen güncelle (Optimistic Update)
    final updatedProgressMap = Map<int, MemberExerciseProgressModel>.from(state.progressByProgram);
    updatedProgressMap[memberWorkoutProgramId] = updatedProgress;

    // Spam önleme için timestamp kaydet
    final updatedToggleMap = Map<int, DateTime>.from(state.lastExerciseToggle);
    updatedToggleMap[exerciseId] = DateTime.now();

    state = state.copyWith(
      progressByProgram: updatedProgressMap,
      lastExerciseToggle: updatedToggleMap,
      clearError: true,
      clearSuccessMessage: true,
    );

    // Arka planda API çağrısı yap
    try {
      final request = CompleteExerciseRequest(
        memberWorkoutProgramID: memberWorkoutProgramId,
        exerciseId: exerciseId,
        isCompleted: isCompleted,
      );

      final result = await _repository.completeExercise(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'API success: Exercise toggled',
          state: 'Exercise: $exerciseId, Completed: $isCompleted',
        );

        // Başarılı - gerçek veriyi yükle (doğrulama için)
        await loadProgressByProgram(memberWorkoutProgramId);

        state = state.copyWith(
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'API error: Reverting optimistic update',
          state: result.message,
        );

        // Hata - optimistic update'i geri al
        final revertedProgressMap = Map<int, MemberExerciseProgressModel>.from(state.progressByProgram);
        revertedProgressMap[memberWorkoutProgramId] = currentProgress;

        state = state.copyWith(
          progressByProgram: revertedProgressMap,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.toggleExercise',
      );

      LoggingService.stateLog(
        'ExerciseProgress',
        'Exception: Reverting optimistic update',
        state: e.toString(),
      );

      // Hata - optimistic update'i geri al
      final revertedProgressMap = Map<int, MemberExerciseProgressModel>.from(state.progressByProgram);
      revertedProgressMap[memberWorkoutProgramId] = currentProgress;

      state = state.copyWith(
        progressByProgram: revertedProgressMap,
        error: 'Egzersiz durumu güncellenirken hata oluştu. Değişiklik geri alındı.',
      );
    }
  }

  /// Spam önleme kontrolü (Optimistic UI için daha esnek)
  bool _canToggleExercise(int exerciseId) {
    final lastToggle = state.lastExerciseToggle[exerciseId];
    if (lastToggle != null) {
      final difference = DateTime.now().difference(lastToggle);
      return difference.inMilliseconds > 500; // 0.5 saniye bekleme (çok hızlı tıklamaları engelle)
    }
    return true;
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Başarı mesajını temizle
  void clearSuccessMessage() {
    state = state.copyWith(clearSuccessMessage: true);
  }

  /// Günü tamamla
  Future<void> completeDay(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Completing day', state: 'Program: $memberWorkoutProgramId');

      state = state.copyWith(
        isUpdating: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final request = CompleteDayRequest(memberWorkoutProgramID: memberWorkoutProgramId);
      final result = await _repository.completeDay(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Day completed successfully',
          state: 'Program: $memberWorkoutProgramId',
        );

        // Progress'i yeniden yükle
        await loadProgressByProgram(memberWorkoutProgramId);

        state = state.copyWith(
          isUpdating: false,
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Failed to complete day',
          state: result.message,
        );

        state = state.copyWith(
          isUpdating: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.completeDay',
      );

      state = state.copyWith(
        isUpdating: false,
        error: 'Gün tamamlanırken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Program sıfırla
  Future<void> resetProgram(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Resetting program', state: 'Program: $memberWorkoutProgramId');

      state = state.copyWith(
        isUpdating: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final request = ResetProgramRequest(memberWorkoutProgramID: memberWorkoutProgramId);
      final result = await _repository.resetProgram(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Program reset successfully',
          state: 'Program: $memberWorkoutProgramId',
        );

        // Progress'i yeniden yükle
        await loadProgressByProgram(memberWorkoutProgramId);

        state = state.copyWith(
          isUpdating: false,
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Failed to reset program',
          state: result.message,
        );

        state = state.copyWith(
          isUpdating: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.resetProgram',
      );

      state = state.copyWith(
        isUpdating: false,
        error: 'Program sıfırlanırken hata oluştu: ${e.toString()}',
      );
    }
  }

  /// Program tamamlama onayı
  Future<void> confirmProgramCompletion(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('ExerciseProgress', 'Confirming program completion', state: 'Program: $memberWorkoutProgramId');

      state = state.copyWith(
        isUpdating: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final request = ConfirmProgramCompletionRequest(memberWorkoutProgramID: memberWorkoutProgramId);
      final result = await _repository.confirmProgramCompletion(request);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Program completion confirmed successfully',
          state: 'Program: $memberWorkoutProgramId',
        );

        // Progress'i yeniden yükle
        await loadProgressByProgram(memberWorkoutProgramId);

        state = state.copyWith(
          isUpdating: false,
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'ExerciseProgress',
          'Failed to confirm program completion',
          state: result.message,
        );

        state = state.copyWith(
          isUpdating: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ExerciseProgressNotifier.confirmProgramCompletion',
      );

      state = state.copyWith(
        isUpdating: false,
        error: 'Program onaylanırken hata oluştu: ${e.toString()}',
      );
    }
  }
}

/// Providers
final exerciseProgressProvider = StateNotifierProvider<ExerciseProgressNotifier, ExerciseProgressState>((ref) {
  final repository = ref.read(exerciseProgressRepositoryProvider);
  return ExerciseProgressNotifier(repository);
});

/// Helper Providers
final exerciseProgressLoadingProvider = Provider<bool>((ref) {
  return ref.watch(exerciseProgressProvider).isLoading;
});

final exerciseProgressRefreshingProvider = Provider<bool>((ref) {
  return ref.watch(exerciseProgressProvider).isRefreshing;
});

final exerciseProgressUpdatingProvider = Provider<bool>((ref) {
  return ref.watch(exerciseProgressProvider).isUpdating;
});

final exerciseProgressErrorProvider = Provider<String?>((ref) {
  return ref.watch(exerciseProgressProvider).error;
});

final exerciseProgressSuccessProvider = Provider<String?>((ref) {
  return ref.watch(exerciseProgressProvider).successMessage;
});

final hasAnyProgressProvider = Provider<bool>((ref) {
  return ref.watch(exerciseProgressProvider).hasAnyProgress;
});

final completedProgramCountProvider = Provider<int>((ref) {
  return ref.watch(exerciseProgressProvider).completedProgramCount;
});

final activeProgramCountProvider = Provider<int>((ref) {
  return ref.watch(exerciseProgressProvider).activeProgramCount;
});

/// Belirli programa ait progress provider
final progressByProgramProvider = Provider.family<MemberExerciseProgressModel?, int>((ref, memberWorkoutProgramId) {
  return ref.watch(exerciseProgressProvider).getProgressForProgram(memberWorkoutProgramId);
});

/// Egzersiz tamamlanma durumu provider
final exerciseCompletionProvider = Provider.family<bool, ({int programId, int exerciseId})>((ref, params) {
  final progress = ref.watch(progressByProgramProvider(params.programId));
  return progress?.isExerciseCompleted(params.exerciseId) ?? false;
});

/// Spam önleme provider (Optimistic UI için daha esnek)
final canToggleExerciseProvider = Provider.family<bool, int>((ref, exerciseId) {
  final lastToggleMap = ref.watch(exerciseProgressProvider).lastExerciseToggle;
  final lastToggle = lastToggleMap[exerciseId];
  if (lastToggle != null) {
    final difference = DateTime.now().difference(lastToggle);
    return difference.inMilliseconds > 500; // 0.5 saniye bekleme (çok hızlı tıklamaları engelle)
  }
  return true;
});
